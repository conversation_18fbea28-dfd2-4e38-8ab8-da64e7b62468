#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
集中管理所有配置项，包括API密钥、超时时间、重试次数等
"""

import os
from typing import Dict, Any


class Config:
    """配置管理类"""
    
    # ==================== API配置 ====================
    # B站API配置 - 从环境变量读取，如果没有则使用默认值
    BILIBILI_APPKEY = os.getenv('BILIBILI_APPKEY', '783bbb7264451d82')
    BILIBILI_APPSEC = os.getenv('BILIBILI_APPSEC', '2653583c8873dea268ab9386918b1d65')
    
    # 答题专用API配置
    ANSWER_APPKEY = os.getenv('ANSWER_APPKEY', '1d8b6e7d45233436')
    ANSWER_APPSEC = os.getenv('ANSWER_APPSEC', '560c52ccd288fed045859ed18bffd973')
    
    # ==================== 网络配置 ====================
    # 请求超时配置
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))
    CAPTCHA_TIMEOUT = int(os.getenv('CAPTCHA_TIMEOUT', '60'))
    
    # 重试配置
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
    RETRY_DELAY_MIN = float(os.getenv('RETRY_DELAY_MIN', '1.0'))
    RETRY_DELAY_MAX = float(os.getenv('RETRY_DELAY_MAX', '3.0'))
    
    # 代理配置
    PROXY_TEST_TIMEOUT = int(os.getenv('PROXY_TEST_TIMEOUT', '10'))
    PROXY_MAX_RETRY_COUNT = int(os.getenv('PROXY_MAX_RETRY_COUNT', '3'))
    
    # ==================== 数据库配置 ====================
    # 数据库文件路径
    ACCOUNT_DB_PATH = os.getenv('ACCOUNT_DB_PATH', 'account_records.db')
    DEVICE_DB_PATH = os.getenv('DEVICE_DB_PATH', 'device_database.db')
    
    # 数据库连接配置
    DB_TIMEOUT = int(os.getenv('DB_TIMEOUT', '30'))
    
    # ==================== 线程配置 ====================
    # 线程管理配置
    THREAD_QUIT_TIMEOUT = int(os.getenv('THREAD_QUIT_TIMEOUT', '5000'))  # 毫秒
    THREAD_TERMINATE_TIMEOUT = int(os.getenv('THREAD_TERMINATE_TIMEOUT', '1000'))  # 毫秒
    
    # ==================== GUI配置 ====================
    # 界面配置
    WINDOW_MIN_WIDTH = int(os.getenv('WINDOW_MIN_WIDTH', '1000'))
    WINDOW_MIN_HEIGHT = int(os.getenv('WINDOW_MIN_HEIGHT', '600'))
    
    # 日志配置
    MAX_LOG_LINES = int(os.getenv('MAX_LOG_LINES', '1000'))
    
    # ==================== 设备模拟配置 ====================
    # 设备指纹配置
    DEVICE_CACHE_ENABLED = os.getenv('DEVICE_CACHE_ENABLED', 'true').lower() == 'true'
    TLS_FINGERPRINT_ENABLED = os.getenv('TLS_FINGERPRINT_ENABLED', 'true').lower() == 'true'
    
    # ==================== 安全配置 ====================
    # 验证码配置
    CAPTCHA_MAX_ATTEMPTS = int(os.getenv('CAPTCHA_MAX_ATTEMPTS', '3'))
    SMS_CODE_LENGTH = int(os.getenv('SMS_CODE_LENGTH', '6'))
    
    # ==================== 调试配置 ====================
    # 调试模式
    DEBUG_MODE = os.getenv('DEBUG_MODE', 'false').lower() == 'true'
    VERBOSE_LOGGING = os.getenv('VERBOSE_LOGGING', 'false').lower() == 'true'
    
    # ==================== URL配置 ====================
    # B站API地址
    BILIBILI_BASE_URL = "https://passport.bilibili.com"
    SMS_SEND_URL = f"{BILIBILI_BASE_URL}/x/passport-login/sms/send"
    SMS_LOGIN_URL = f"{BILIBILI_BASE_URL}/x/passport-login/web/sms/login"
    CAPTCHA_VERIFY_URL = f"{BILIBILI_BASE_URL}/x/passport-login/captcha/verify"
    
    # 网络检测URL
    NETWORK_TEST_URLS = [
        "https://www.baidu.com",
        "https://api.bilibili.com/x/web-interface/nav",
        "https://www.qq.com",
        "https://httpbin.org/get",
        "https://www.google.com"
    ]
    
    # IP检测API
    IP_DETECTION_APIS = [
        "http://ip-api.com/json/?fields=isp,org,as",
        "https://ipapi.co/json/",
        "https://api.ipify.org?format=json"
    ]
    
    @classmethod
    def get_all_config(cls) -> Dict[str, Any]:
        """获取所有配置项"""
        config_dict = {}
        for attr_name in dir(cls):
            if not attr_name.startswith('_') and not callable(getattr(cls, attr_name)):
                config_dict[attr_name] = getattr(cls, attr_name)
        return config_dict
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置项的有效性"""
        try:
            # 验证必要的配置项
            assert cls.BILIBILI_APPKEY, "BILIBILI_APPKEY不能为空"
            assert cls.BILIBILI_APPSEC, "BILIBILI_APPSEC不能为空"
            assert cls.REQUEST_TIMEOUT > 0, "REQUEST_TIMEOUT必须大于0"
            assert cls.MAX_RETRIES > 0, "MAX_RETRIES必须大于0"
            assert cls.THREAD_QUIT_TIMEOUT > 0, "THREAD_QUIT_TIMEOUT必须大于0"
            
            return True
        except AssertionError as e:
            print(f"配置验证失败: {e}")
            return False
    
    @classmethod
    def print_config(cls):
        """打印当前配置（隐藏敏感信息）"""
        print("=" * 50)
        print("当前配置信息:")
        print("=" * 50)
        
        config = cls.get_all_config()
        sensitive_keys = ['APPSEC', 'SECRET', 'PASSWORD', 'TOKEN']
        
        for key, value in sorted(config.items()):
            # 隐藏敏感信息
            if any(sensitive in key.upper() for sensitive in sensitive_keys):
                if isinstance(value, str) and len(value) > 10:
                    display_value = f"{value[:4]}...{value[-4:]}"
                else:
                    display_value = "***"
            else:
                display_value = value
            
            print(f"{key}: {display_value}")
        
        print("=" * 50)


# 创建全局配置实例
config = Config()

# 验证配置
if not config.validate_config():
    print("⚠️ 配置验证失败，请检查配置项")

# 如果是调试模式，打印配置信息
if config.DEBUG_MODE:
    config.print_config()


# 导出常用配置项（向后兼容）
BILIBILI_APPKEY = config.BILIBILI_APPKEY
BILIBILI_APPSEC = config.BILIBILI_APPSEC
REQUEST_TIMEOUT = config.REQUEST_TIMEOUT
MAX_RETRIES = config.MAX_RETRIES


if __name__ == "__main__":
    # 测试配置模块
    print("🔧 配置模块测试")
    config.print_config()
    
    # 测试配置验证
    if config.validate_config():
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败")
