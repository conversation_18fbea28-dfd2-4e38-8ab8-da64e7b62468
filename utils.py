#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用工具类模块
提取项目中的通用工具方法，避免代码重复
"""

import socket
import requests
import sqlite3
from contextlib import contextmanager
from typing import Dict, Any, Optional
from config import config


class NetworkUtils:
    """网络相关工具类"""
    
    @staticmethod
    def test_dns_resolution(hostname: str = "passport.bilibili.com") -> bool:
        """测试DNS解析"""
        try:
            socket.gethostbyname(hostname)
            return True
        except socket.gaierror:
            return False
    
    @staticmethod
    def test_tcp_connection(hostname: str = "passport.bilibili.com", port: int = 443, timeout: int = 5) -> bool:
        """测试TCP连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((hostname, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    @staticmethod
    def test_network_connectivity() -> Dict[str, bool]:
        """测试网络连接性"""
        results = {}
        
        # 测试DNS解析
        results['dns'] = NetworkUtils.test_dns_resolution()
        
        # 测试TCP连接
        results['tcp'] = NetworkUtils.test_tcp_connection()
        
        # 测试HTTP连接
        results['http'] = False
        for url in config.NETWORK_TEST_URLS:
            try:
                response = requests.get(url, timeout=8)
                if response.status_code == 200:
                    results['http'] = True
                    break
            except Exception:
                continue
        
        return results


class ValidationUtils:
    """验证相关工具类"""
    
    @staticmethod
    def check_high_risk_account(data: Dict[str, Any]) -> Dict[str, Any]:
        """检查是否为高危账号"""
        if data.get('status') == 2 and '高危异常行为' in str(data.get('message', '')):
            return {
                'is_high_risk': True,
                'message': data.get('message', '账号存在高危异常行为'),
                'url': data.get('url', '')
            }
        return {'is_high_risk': False}
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """验证手机号格式"""
        if not phone:
            return False
        
        # 移除所有非数字字符
        phone_digits = ''.join(filter(str.isdigit, phone))
        
        # 检查长度（中国手机号11位）
        if len(phone_digits) != 11:
            return False
        
        # 检查是否以1开头
        if not phone_digits.startswith('1'):
            return False
        
        return True
    
    @staticmethod
    def validate_country_code(code: str) -> bool:
        """验证国家代码格式"""
        if not code:
            return False
        
        # 移除+号
        code = code.lstrip('+')
        
        # 检查是否全为数字
        if not code.isdigit():
            return False
        
        # 检查长度（1-4位）
        if not (1 <= len(code) <= 4):
            return False
        
        return True
    
    @staticmethod
    def validate_sms_code(code: str) -> bool:
        """验证短信验证码格式"""
        if not code:
            return False
        
        # 移除所有非数字字符
        code_digits = ''.join(filter(str.isdigit, code))
        
        # 检查长度
        return len(code_digits) == config.SMS_CODE_LENGTH


class StringUtils:
    """字符串处理工具类"""
    
    @staticmethod
    def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
        """截断字符串"""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def mask_sensitive_info(text: str, show_chars: int = 4) -> str:
        """遮蔽敏感信息"""
        if not text or len(text) <= show_chars * 2:
            return "***"
        
        return f"{text[:show_chars]}...{text[-show_chars:]}"
    
    @staticmethod
    def format_phone_number(phone: str, country_code: str = "+86") -> str:
        """格式化手机号显示"""
        if not phone:
            return ""
        
        # 移除所有非数字字符
        phone_digits = ''.join(filter(str.isdigit, phone))
        
        if len(phone_digits) == 11:
            # 中国手机号格式：138****1234
            return f"{phone_digits[:3]}****{phone_digits[-4:]}"
        
        return StringUtils.mask_sensitive_info(phone_digits)


class LogUtils:
    """日志相关工具类"""
    
    @staticmethod
    def format_log_message(level: str, message: str, prefix: str = "") -> str:
        """格式化日志消息"""
        level_icons = {
            'INFO': '📝',
            'SUCCESS': '✅',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'DEBUG': '🔍'
        }
        
        icon = level_icons.get(level.upper(), '📝')
        
        if prefix:
            return f"{icon} {prefix}: {message}"
        else:
            return f"{icon} {message}"
    
    @staticmethod
    def format_response_log(response_data: Dict[str, Any], max_length: int = 200) -> str:
        """格式化响应数据日志"""
        import json
        
        try:
            json_str = json.dumps(response_data, ensure_ascii=False, indent=2)
            if len(json_str) > max_length:
                return StringUtils.truncate_string(json_str, max_length)
            return json_str
        except Exception:
            return str(response_data)


class ProxyUtils:
    """代理相关工具类"""
    
    @staticmethod
    def parse_proxy_string(proxy_str: str) -> Optional[Dict[str, str]]:
        """解析代理字符串"""
        if not proxy_str or ':' not in proxy_str:
            return None
        
        try:
            ip, port = proxy_str.split(':', 1)
            return {
                'ip': ip.strip(),
                'port': port.strip(),
                'protocol': 'socks5'
            }
        except ValueError:
            return None
    
    @staticmethod
    def format_proxy_info(proxy_info: Dict[str, str]) -> str:
        """格式化代理信息显示"""
        if not proxy_info:
            return "无代理"
        
        return f"{proxy_info.get('ip', 'Unknown')}:{proxy_info.get('port', 'Unknown')} ({proxy_info.get('protocol', 'Unknown')})"


class TimeUtils:
    """时间相关工具类"""
    
    @staticmethod
    def get_current_timestamp() -> int:
        """获取当前时间戳（秒）"""
        import time
        return int(time.time())
    
    @staticmethod
    def get_current_timestamp_ms() -> int:
        """获取当前时间戳（毫秒）"""
        import time
        return int(time.time() * 1000)
    
    @staticmethod
    def format_datetime(timestamp: Optional[int] = None) -> str:
        """格式化日期时间"""
        import datetime
        
        if timestamp is None:
            dt = datetime.datetime.now()
        else:
            dt = datetime.datetime.fromtimestamp(timestamp)
        
        return dt.strftime("%Y-%m-%d %H:%M:%S")


class DatabaseUtils:
    """数据库相关工具类"""

    @staticmethod
    @contextmanager
    def get_db_connection(db_path: str):
        """数据库连接上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(db_path, timeout=config.DB_TIMEOUT)
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()

    @staticmethod
    def execute_query(db_path: str, query: str, params: tuple = None, fetch_one: bool = False, fetch_all: bool = False):
        """执行数据库查询"""
        with DatabaseUtils.get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount

    @staticmethod
    def execute_many(db_path: str, query: str, params_list: list):
        """批量执行数据库操作"""
        with DatabaseUtils.get_db_connection(db_path) as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount


# 导出常用工具类实例
network_utils = NetworkUtils()
validation_utils = ValidationUtils()
string_utils = StringUtils()
log_utils = LogUtils()
proxy_utils = ProxyUtils()
time_utils = TimeUtils()
database_utils = DatabaseUtils()


if __name__ == "__main__":
    # 测试工具类
    print("🔧 工具类模块测试")
    
    # 测试网络连接
    print("\n📡 网络连接测试:")
    connectivity = network_utils.test_network_connectivity()
    for test_name, result in connectivity.items():
        status = "✅" if result else "❌"
        print(f"  {test_name}: {status}")
    
    # 测试验证功能
    print("\n🔍 验证功能测试:")
    print(f"  手机号验证 (13800138000): {validation_utils.validate_phone_number('13800138000')}")
    print(f"  国家代码验证 (+86): {validation_utils.validate_country_code('+86')}")
    print(f"  短信验证码验证 (123456): {validation_utils.validate_sms_code('123456')}")
    
    # 测试字符串工具
    print("\n📝 字符串工具测试:")
    long_text = "这是一个很长的字符串，用来测试截断功能"
    print(f"  截断前: {long_text}")
    print(f"  截断后: {string_utils.truncate_string(long_text, 20)}")
    print(f"  敏感信息遮蔽: {string_utils.mask_sensitive_info('1234567890abcdef')}")
