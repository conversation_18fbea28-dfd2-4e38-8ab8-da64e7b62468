#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TLS指纹生成器
为每个设备生成唯一的TLS指纹，模拟真实手机设备的TLS握手特征
"""

import hashlib
import random
import json
import ssl
import socket
from urllib3.util.ssl_ import create_urllib3_context
from urllib3.poolmanager import PoolManager
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class TLSFingerprint:
    """TLS指纹生成器"""
    
    def __init__(self, device_info):
        """
        初始化TLS指纹生成器
        
        Args:
            device_info: 设备信息字典，包含品牌、型号、Android版本等
        """
        self.device_info = device_info
        self.fingerprint_data = self._generate_fingerprint_data()
        
    def _generate_fingerprint_data(self):
        """生成TLS指纹数据"""
        # 基于设备信息生成种子
        seed_string = (
            f"{self.device_info.get('imei', '')}"
            f"{self.device_info.get('android_id', '')}"
            f"{self.device_info.get('brand', '')}"
            f"{self.device_info.get('model', '')}"
            f"{self.device_info.get('android_version', '')}"
        )
        
        # 使用设备信息作为随机种子，确保同一设备总是生成相同的指纹
        random.seed(hashlib.sha256(seed_string.encode()).hexdigest())
        
        # Android设备常见的TLS配置
        android_cipher_suites = [
            # TLS 1.3 密码套件
            "TLS_AES_128_GCM_SHA256",
            "TLS_AES_256_GCM_SHA384", 
            "TLS_CHACHA20_POLY1305_SHA256",
            
            # TLS 1.2 密码套件 (Android常用)
            "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
            "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
            "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256",
            "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
            "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
            "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256",
            "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256",
            "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384",
            "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256",
            "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384",
            "TLS_RSA_WITH_AES_128_GCM_SHA256",
            "TLS_RSA_WITH_AES_256_GCM_SHA384",
            "TLS_RSA_WITH_AES_128_CBC_SHA256",
            "TLS_RSA_WITH_AES_256_CBC_SHA256",
        ]
        
        # 支持的椭圆曲线
        supported_curves = [
            "secp256r1",  # P-256
            "secp384r1",  # P-384
            "secp521r1",  # P-521
            "x25519",
            "x448"
        ]
        
        # 支持的签名算法
        signature_algorithms = [
            "ecdsa_secp256r1_sha256",
            "ecdsa_secp384r1_sha384",
            "ecdsa_secp521r1_sha512",
            "rsa_pss_rsae_sha256",
            "rsa_pss_rsae_sha384",
            "rsa_pss_rsae_sha512",
            "rsa_pkcs1_sha256",
            "rsa_pkcs1_sha384",
            "rsa_pkcs1_sha512",
            "ecdsa_sha1",
            "rsa_pkcs1_sha1"
        ]
        
        # 根据Android版本调整TLS配置
        android_version = int(self.device_info.get('android_version', '11'))
        
        # 选择密码套件（基于Android版本和设备特征）
        num_ciphers = random.randint(8, 16)
        selected_ciphers = random.sample(android_cipher_suites, min(num_ciphers, len(android_cipher_suites)))
        
        # 选择椭圆曲线
        num_curves = random.randint(3, 5)
        selected_curves = random.sample(supported_curves, min(num_curves, len(supported_curves)))
        
        # 选择签名算法
        num_sig_algs = random.randint(6, 11)
        selected_sig_algs = random.sample(signature_algorithms, min(num_sig_algs, len(signature_algorithms)))
        
        # 生成TLS扩展
        extensions = self._generate_tls_extensions()
        
        fingerprint_data = {
            "tls_version": "TLS 1.3" if android_version >= 10 else "TLS 1.2",
            "cipher_suites": selected_ciphers,
            "supported_curves": selected_curves,
            "signature_algorithms": selected_sig_algs,
            "extensions": extensions,
            "compression_methods": ["null"],
            "session_id_length": random.randint(0, 32),
            "random_length": 32,
            "device_fingerprint": self._generate_device_tls_fingerprint()
        }
        
        # 重置随机种子
        random.seed()
        
        return fingerprint_data
    
    def _generate_tls_extensions(self):
        """生成TLS扩展"""
        extensions = {
            "server_name": True,  # SNI
            "supported_groups": True,  # 椭圆曲线组
            "signature_algorithms": True,  # 签名算法
            "application_layer_protocol_negotiation": ["h2", "http/1.1"],  # ALPN
            "encrypt_then_mac": True,
            "extended_master_secret": True,
            "session_ticket": True,
            "supported_versions": True,
            "key_share": True,
            "psk_key_exchange_modes": ["psk_dhe_ke"],
            "renegotiation_info": True,
            "status_request": True,  # OCSP stapling
        }
        
        # 根据Android版本添加特定扩展
        android_version = int(self.device_info.get('android_version', '11'))
        
        if android_version >= 10:
            extensions["pre_shared_key"] = True
            extensions["early_data"] = True
            
        if android_version >= 9:
            extensions["certificate_authorities"] = True
            extensions["oid_filters"] = True
            
        return extensions
    
    def _generate_device_tls_fingerprint(self):
        """生成设备特定的TLS指纹哈希"""
        fingerprint_string = (
            f"{self.device_info.get('brand', '')}"
            f"{self.device_info.get('model', '')}"
            f"{self.device_info.get('android_version', '')}"
            f"{self.device_info.get('build_id', '')}"
            f"{self.device_info.get('device_fingerprint', '')}"
        )
        
        return hashlib.sha256(fingerprint_string.encode()).hexdigest()[:16]
    
    def get_ja3_fingerprint(self):
        """
        生成JA3指纹
        JA3是一种TLS客户端指纹识别方法
        """
        # JA3格式: TLSVersion,CipherSuites,Extensions,EllipticCurves,EllipticCurvePointFormats
        
        # TLS版本号
        tls_version = "771" if self.fingerprint_data["tls_version"] == "TLS 1.2" else "772"
        
        # 密码套件转换为数字ID（简化版本）
        cipher_ids = []
        for cipher in self.fingerprint_data["cipher_suites"]:
            # 简化的密码套件ID映射
            cipher_hash = hashlib.md5(cipher.encode()).hexdigest()[:4]
            cipher_ids.append(str(int(cipher_hash, 16) % 65535))
        
        # 扩展ID
        extension_ids = ["0", "5", "10", "11", "13", "16", "18", "21", "23", "35", "43", "45", "51"]
        
        # 椭圆曲线ID
        curve_ids = ["23", "24", "25", "29", "30"]  # 常见椭圆曲线的ID
        
        # 椭圆曲线点格式
        point_formats = ["0"]  # uncompressed
        
        ja3_string = f"{tls_version},{'-'.join(cipher_ids)},{'-'.join(extension_ids)},{'-'.join(curve_ids)},{'-'.join(point_formats)}"
        
        # 生成JA3哈希
        ja3_hash = hashlib.md5(ja3_string.encode()).hexdigest()
        
        return {
            "ja3_string": ja3_string,
            "ja3_hash": ja3_hash
        }
    
    def get_fingerprint_summary(self):
        """获取指纹摘要信息"""
        ja3 = self.get_ja3_fingerprint()

        return {
            "device_tls_fingerprint": self.fingerprint_data["device_fingerprint"],
            "ja3_hash": ja3["ja3_hash"],
            "ja3_string": ja3["ja3_string"],
            "tls_version": self.fingerprint_data["tls_version"],
            "cipher_count": len(self.fingerprint_data["cipher_suites"]),
            "curve_count": len(self.fingerprint_data["supported_curves"]),
            "extension_count": len(self.fingerprint_data["extensions"]),
            "signature_algorithm_count": len(self.fingerprint_data["signature_algorithms"])
        }

    def get_detailed_fingerprint_info(self):
        """获取详细的TLS指纹信息"""
        ja3 = self.get_ja3_fingerprint()

        return {
            "basic_info": {
                "device_tls_fingerprint": self.fingerprint_data["device_fingerprint"],
                "ja3_hash": ja3["ja3_hash"],
                "ja3_string": ja3["ja3_string"],
                "tls_version": self.fingerprint_data["tls_version"]
            },
            "cipher_suites": {
                "count": len(self.fingerprint_data["cipher_suites"]),
                "list": self.fingerprint_data["cipher_suites"]
            },
            "supported_curves": {
                "count": len(self.fingerprint_data["supported_curves"]),
                "list": self.fingerprint_data["supported_curves"]
            },
            "signature_algorithms": {
                "count": len(self.fingerprint_data["signature_algorithms"]),
                "list": self.fingerprint_data["signature_algorithms"]
            },
            "extensions": {
                "count": len(self.fingerprint_data["extensions"]),
                "list": list(self.fingerprint_data["extensions"].keys())
            },
            "other_info": {
                "compression_methods": self.fingerprint_data["compression_methods"],
                "session_id_length": self.fingerprint_data["session_id_length"],
                "random_length": self.fingerprint_data["random_length"]
            }
        }


class TLSAdapter(HTTPAdapter):
    """自定义TLS适配器，应用TLS指纹"""
    
    def __init__(self, tls_fingerprint, *args, **kwargs):
        self.tls_fingerprint = tls_fingerprint
        super().__init__(*args, **kwargs)
    
    def init_poolmanager(self, *args, **kwargs):
        """初始化连接池管理器，应用自定义TLS配置"""
        # 创建自定义SSL上下文
        ssl_context = self._create_custom_ssl_context()
        
        kwargs['ssl_context'] = ssl_context
        return super().init_poolmanager(*args, **kwargs)
    
    def _create_custom_ssl_context(self):
        """创建自定义SSL上下文"""
        try:
            # 创建基础SSL上下文
            context = create_urllib3_context()

            # 应用TLS指纹配置
            fingerprint_data = self.tls_fingerprint.fingerprint_data

            # 设置TLS版本 - 更宽松的配置
            try:
                if fingerprint_data["tls_version"] == "TLS 1.3":
                    context.minimum_version = ssl.TLSVersion.TLSv1_2
                    context.maximum_version = ssl.TLSVersion.TLSv1_3
                else:
                    context.minimum_version = ssl.TLSVersion.TLSv1_2
                    context.maximum_version = ssl.TLSVersion.TLSv1_3  # 允许更高版本
            except AttributeError:
                # 旧版本Python的兼容性处理
                context.protocol = ssl.PROTOCOL_TLS

            # 设置密码套件 - 更兼容的配置
            try:
                # 使用更广泛兼容的密码套件
                context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:ECDHE+AES:DHE+AES:RSA+AESGCM:RSA+AES:!aNULL:!eNULL:!MD5:!DSS')
            except:
                # 如果设置失败，使用默认配置
                try:
                    context.set_ciphers('HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA')
                except:
                    pass

            # 设置其他SSL选项以提高兼容性
            context.check_hostname = True
            context.verify_mode = ssl.CERT_REQUIRED

            # 加载默认CA证书
            try:
                context.load_default_certs()
            except:
                # 如果加载默认证书失败，降低验证要求
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE

            return context

        except Exception as e:
            # 如果自定义SSL上下文创建失败，返回默认上下文
            print(f"⚠️ TLS指纹配置失败，使用默认SSL配置: {e}")
            return create_urllib3_context()


def test_network_connectivity():
    """测试网络连接性"""
    test_urls = [
        "https://www.baidu.com",  # 国内最稳定
        "https://api.bilibili.com/x/web-interface/nav",  # B站API
        "https://www.qq.com",  # 腾讯服务
        "https://httpbin.org/get",  # 国外服务（备用）
        "https://www.google.com"  # 最后备用
    ]

    print("🔍 测试网络连接性...")

    for url in test_urls:
        try:
            response = requests.get(url, timeout=8)  # 增加超时时间
            if response.status_code == 200:
                print(f"✅ {url} - 连接成功")
                return True
            else:
                print(f"⚠️ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 连接失败: {e}")

    print("❌ 所有测试URL都无法连接")
    return False


def create_tls_session(device_info, enable_fallback=True):
    """
    创建带有TLS指纹的requests会话

    Args:
        device_info: 设备信息字典
        enable_fallback: 是否启用回退机制

    Returns:
        配置了TLS指纹的requests.Session对象
    """
    try:
        # 生成TLS指纹
        tls_fingerprint = TLSFingerprint(device_info)

        # 创建会话
        session = requests.Session()

        # 创建TLS适配器
        tls_adapter = TLSAdapter(tls_fingerprint)

        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )

        # 创建带重试的适配器
        tls_adapter_with_retry = TLSAdapter(tls_fingerprint, max_retries=retry_strategy)

        # 挂载适配器
        session.mount('https://', tls_adapter_with_retry)
        session.mount('http://', tls_adapter_with_retry)

        # 测试TLS会话是否工作 - 使用多个备用测试服务
        test_urls = [
            "https://www.baidu.com",  # 国内稳定服务
            "https://api.bilibili.com/x/web-interface/nav",  # B站API
            "https://httpbin.org/get",  # 原测试服务（作为备用）
        ]

        test_success = False
        for test_url in test_urls:
            try:
                test_response = session.get(test_url, timeout=10)
                if test_response.status_code == 200:
                    print(f"✅ TLS指纹会话测试成功 (使用 {test_url})")
                    test_success = True
                    break
                else:
                    print(f"⚠️ TLS测试失败 {test_url}，状态码: {test_response.status_code}")
            except Exception as e:
                print(f"⚠️ TLS测试失败 {test_url}: {str(e)[:100]}...")
                continue

        if test_success:
            return session, tls_fingerprint
        else:
            print("⚠️ 所有TLS指纹测试服务都失败")

        # 如果测试失败且启用回退，返回普通会话
        if enable_fallback:
            print("🔄 回退到普通requests会话")
            fallback_session = requests.Session()
            fallback_session.mount('https://', requests.adapters.HTTPAdapter(max_retries=retry_strategy))
            fallback_session.mount('http://', requests.adapters.HTTPAdapter(max_retries=retry_strategy))
            return fallback_session, tls_fingerprint
        else:
            return session, tls_fingerprint

    except Exception as e:
        print(f"❌ TLS指纹会话创建失败: {e}")

        if enable_fallback:
            print("🔄 回退到普通requests会话")
            # 创建普通会话作为备用
            fallback_session = requests.Session()
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
            )
            fallback_session.mount('https://', requests.adapters.HTTPAdapter(max_retries=retry_strategy))
            fallback_session.mount('http://', requests.adapters.HTTPAdapter(max_retries=retry_strategy))

            # 创建一个简化的TLS指纹对象
            try:
                tls_fingerprint = TLSFingerprint(device_info)
            except:
                tls_fingerprint = None

            return fallback_session, tls_fingerprint
        else:
            raise


if __name__ == "__main__":
    # 测试代码
    test_device_info = {
        'imei': '123456789012345',
        'android_id': 'test_android_id',
        'brand': 'OnePlus',
        'model': 'PJD110',
        'android_version': '11',
        'build_id': 'test_build',
        'device_fingerprint': 'test_fingerprint'
    }
    
    # 创建TLS指纹
    tls_fp = TLSFingerprint(test_device_info)
    
    # 显示指纹信息
    summary = tls_fp.get_fingerprint_summary()
    print("TLS指纹摘要:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 显示JA3指纹
    ja3 = tls_fp.get_ja3_fingerprint()
    print(f"\nJA3指纹: {ja3['ja3_hash']}")
    print(f"JA3字符串: {ja3['ja3_string']}")
