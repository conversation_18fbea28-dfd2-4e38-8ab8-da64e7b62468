#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android设备指纹生成器
实现真实的Android设备信息生成，包括IMEI、Android ID、MAC地址等
基于真实设备数据库和算法生成具有唯一性的设备指纹
"""

import hashlib
import random
import time
import uuid
import sqlite3
import os
from typing import Dict, List, Tuple
import datetime


class AndroidDeviceFingerprint:
    """Android设备指纹生成器"""
    
    def __init__(self):
        self.db_path = "device_database.db"
        self.init_database()
        self.device_brands = {
            "Xiaomi": ["M2004J19C", "M2102J2SC", "M2101K9C", "M2007J3SY", "M2012K11AC"],
            "Samsung": ["SM-G998B", "SM-G996B", "SM-G991B", "SM-N986B", "SM-A525F"],
            "OPPO": ["PJD110", "PCLM10", "CPH2173", "CPH2127", "CPH2185"],
            "Vivo": ["V2055A", "V2031A", "V2046A", "V2025A", "V2020A"],
            "Huawei": ["ELS-AN00", "NOH-AN00", "TAS-AN00", "ANA-AN00", "LIO-AN00"],
            "OnePlus": ["LE2100", "LE2110", "IN2020", "HD1900", "HD1905"]
        }
        self.android_versions = ["11", "12", "13", "14", "15"]

        # 真实的浏览器版本映射表 - 基于Android版本对应的真实Chrome版本
        self.browser_versions = {
            "11": {
                "chrome_versions": ["91.0.4472.120", "92.0.4515.159", "93.0.4577.82", "94.0.4606.85"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36",
                "sdk_int": "30"
            },
            "12": {
                "chrome_versions": ["100.0.4896.127", "101.0.4951.64", "102.0.5005.125", "103.0.5060.129"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36",
                "sdk_int": "31"
            },
            "13": {
                "chrome_versions": ["108.0.5359.128", "109.0.5414.118", "110.0.5481.154", "111.0.5563.116"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36",
                "sdk_int": "33"
            },
            "14": {
                "chrome_versions": ["120.0.6099.210", "121.0.6167.178", "122.0.6261.119", "123.0.6312.118"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36",
                "sdk_int": "34"
            },
            "15": {
                "chrome_versions": ["131.0.6778.200", "132.0.6834.83", "133.0.6846.114", "134.0.6847.114"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36",
                "sdk_int": "35"
            }
        }

        # 真实的构建版本映射 - 基于Android版本的真实构建标识
        self.build_versions = {
            "11": ["RP1A.200720.011", "RQ3A.210905.001", "RP1A.201005.004"],
            "12": ["SQ3A.220705.004", "SP2A.220405.004", "SQ1D.220205.004"],
            "13": ["TQ3A.230805.001", "TP1A.220624.014", "TQ2A.230505.002"],
            "14": ["UQ1A.240205.004", "UP1A.231005.007", "UQ1A.240105.004"],
            "15": ["VQ3A.240605.005", "VP2A.240505.005", "VQ1A.240105.002"]
        }

        # 真实的浏览器版本映射表
        self.browser_versions = {
            "11": {
                "chrome_versions": ["91.0.4472.120", "92.0.4515.159", "93.0.4577.82"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36"
            },
            "12": {
                "chrome_versions": ["100.0.4896.127", "101.0.4951.64", "102.0.5005.125"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36"
            },
            "13": {
                "chrome_versions": ["108.0.5359.128", "109.0.5414.118", "110.0.5481.154"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36"
            },
            "14": {
                "chrome_versions": ["120.0.6099.210", "121.0.6167.178", "122.0.6261.119"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36"
            },
            "15": {
                "chrome_versions": ["131.0.6778.200", "132.0.6834.83", "133.0.6846.114"],
                "webkit_version": "537.36",
                "webview_version": "4.0",
                "safari_version": "537.36"
            }
        }
        
    def init_database(self):
        """初始化设备数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建设备表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS devices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_fingerprint TEXT UNIQUE,
                brand TEXT,
                model TEXT,
                android_version TEXT,
                imei TEXT UNIQUE,
                android_id TEXT UNIQUE,
                mac_address TEXT UNIQUE,
                serial_number TEXT UNIQUE,
                cpu_abi TEXT,
                screen_resolution TEXT,
                memory_total TEXT,
                storage_total TEXT,
                build_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 检查并添加新字段（为了兼容旧数据库）
        try:
            cursor.execute("ALTER TABLE devices ADD COLUMN storage_total TEXT")
        except sqlite3.OperationalError:
            pass  # 字段已存在

        try:
            cursor.execute("ALTER TABLE devices ADD COLUMN build_id TEXT")
        except sqlite3.OperationalError:
            pass  # 字段已存在

        conn.commit()
        conn.close()
    
    def generate_imei(self, brand: str = None) -> str:
        """
        生成更真实的IMEI号码，使用真实厂商TAC
        根据品牌选择对应的TAC范围，提高真实性
        """
        # 按品牌分类的真实TAC列表 - 从真实设备收集
        brand_tac_map = {
            "Samsung": ["35216406", "35328708", "35404908", "35503309"],
            "Xiaomi": ["86891303", "86891304", "86891305", "86891306"],
            "OPPO": ["35847709", "35875107", "35679804", "35404909"],
            "Vivo": ["86801004", "86801005", "86801006", "86801007"],
            "Huawei": ["86801003", "86801008", "86801009", "86801010"],
            "OnePlus": ["86891307", "86891308", "35216407", "35328709"],
        }

        # 根据品牌选择TAC，如果品牌不在列表中则使用通用TAC
        if brand and brand in brand_tac_map:
            tac_list = brand_tac_map[brand]
        else:
            # 通用TAC列表
            tac_list = ["35503310", "35679805", "35875108", "35404910"]

        tac = random.choice(tac_list)

        # 生成更真实的序列号（6位）
        # 使用更真实的序列号模式
        serial_patterns = [
            lambda: ''.join([str(random.randint(0, 9)) for _ in range(6)]),  # 纯随机
            lambda: f"{random.randint(100, 999)}{random.randint(100, 999)}",  # 两个3位数
            lambda: f"0{random.randint(10000, 99999)}",  # 以0开头
            lambda: f"{random.randint(1, 9)}{random.randint(10000, 99999)}"  # 非0开头
        ]
        serial = random.choice(serial_patterns)()

        # 计算校验位（Luhn算法）
        imei_without_check = tac + serial
        check_digit = self._calculate_luhn_check_digit(imei_without_check)

        return imei_without_check + str(check_digit)
    
    def _calculate_luhn_check_digit(self, number: str) -> int:
        """计算Luhn校验位"""
        digits = [int(d) for d in number]
        for i in range(len(digits) - 1, -1, -2):
            digits[i] *= 2
            if digits[i] > 9:
                digits[i] = digits[i] // 10 + digits[i] % 10
        
        total = sum(digits)
        return (10 - (total % 10)) % 10
    
    def generate_android_id(self) -> str:
        """生成Android ID"""
        # Android ID是64位十六进制字符串
        return ''.join(random.choices('0123456789abcdef', k=16))
    
    def generate_mac_address(self) -> str:
        """生成MAC地址"""
        # 生成6个字节的MAC地址
        mac_bytes = [random.randint(0x00, 0xff) for _ in range(6)]
        # 确保第一个字节的最低位为0（单播地址）
        mac_bytes[0] &= 0xfe
        return ':'.join([f'{b:02x}' for b in mac_bytes])

    def generate_browser_info(self, android_version: str) -> Dict:
        """
        根据Android版本生成真实的浏览器信息
        包括Chrome版本、WebKit版本、构建版本等
        支持真实的Mozilla和WebKit版本随机化
        """
        # SDK版本映射
        sdk_mapping = {
            "11": "30",
            "12": "31",
            "13": "33",
            "14": "34",
            "15": "35"
        }

        # Chrome版本映射 - 基于真实的Android WebView版本
        chrome_mapping = {
            "11": ["91.0.4472.120", "92.0.4515.159", "93.0.4577.82", "94.0.4606.85"],
            "12": ["100.0.4896.127", "101.0.4951.64", "102.0.5005.125", "103.0.5060.129"],
            "13": ["108.0.5359.128", "109.0.5414.118", "110.0.5481.154", "111.0.5563.116"],
            "14": ["120.0.6099.210", "121.0.6167.178", "122.0.6261.119", "123.0.6312.118"],
            "15": ["131.0.6778.200", "132.0.6834.83", "133.0.6846.114", "134.0.6847.114"]
        }

        # Mozilla版本映射 - 基于真实的浏览器版本历史资料
        # Mozilla/5.0是绝对标准(~95%)，但添加一些真实存在的历史版本
        mozilla_versions = [
            "5.0", "5.0", "5.0", "5.0", "5.0",  # Mozilla/5.0 - 现代浏览器标准(权重5)
            "5.0", "5.0", "5.0", "5.0", "5.0",  # 继续增加5.0权重(权重5)
            "4.0"  # Mozilla/4.0 - 历史上IE和一些老版本浏览器使用，极少但真实存在
        ]

        # WebKit版本映射 - 基于真实的Android Chrome WebKit版本历史资料
        # 添加真实存在的WebKit版本变化，避免固定版本被检测
        # 基于查阅的资料，WebKit版本历史确实有一些变化
        webkit_mapping = {
            "11": [
                "537.36", "537.36", "537.36", "537.36",  # 537.36主流(权重4)
                "534.30"  # 历史版本，Android 4.3时期使用，极少但真实存在
            ],
            "12": [
                "537.36", "537.36", "537.36", "537.36",  # 537.36主流(权重4)
                "535.19"  # 历史版本，一些老设备可能仍在使用
            ],
            "13": [
                "537.36", "537.36", "537.36", "537.36",  # 537.36主流(权重4)
                "536.26"  # 历史版本，iPhone 6时期的WebKit版本，偶尔出现
            ],
            "14": [
                "537.36", "537.36", "537.36", "537.36",  # 537.36主流(权重4)
                "605.1.15"  # Safari相关的WebKit版本，极少在Android上出现但真实存在
            ],
            "15": [
                "537.36", "537.36", "537.36", "537.36",  # 537.36主流(权重4)
                "534.30"  # 历史版本，增加变化性
            ]
        }

        # Safari版本映射 - 基于WebKit版本对应的Safari版本
        # 在Android Chrome中，Safari版本标识与WebKit版本保持一致
        # 与WebKit版本同步变化，保持真实性
        safari_mapping = {
            "11": [
                "537.36", "537.36", "537.36", "537.36",  # 主流版本(权重4)
                "534.30"  # 对应WebKit 534.30
            ],
            "12": [
                "537.36", "537.36", "537.36", "537.36",  # 主流版本(权重4)
                "535.19"  # 对应WebKit 535.19
            ],
            "13": [
                "537.36", "537.36", "537.36", "537.36",  # 主流版本(权重4)
                "536.26"  # 对应WebKit 536.26
            ],
            "14": [
                "537.36", "537.36", "537.36", "537.36",  # 主流版本(权重4)
                "605.1.15"  # 对应WebKit 605.1.15
            ],
            "15": [
                "537.36", "537.36", "537.36", "537.36",  # 主流版本(权重4)
                "534.30"  # 对应WebKit 534.30
            ]
        }

        # WebView版本映射 - Android WebView版本历史
        webview_mapping = {
            "11": ["4.0"],
            "12": ["4.0"],
            "13": ["4.0"],
            "14": ["4.0"],
            "15": ["4.0"]
        }

        if android_version not in chrome_mapping:
            android_version = "15"  # 默认使用最新版本

        # 随机选择对应Android版本的各种浏览器版本
        chrome_version = random.choice(chrome_mapping[android_version])
        webkit_version = random.choice(webkit_mapping[android_version])
        # Safari版本应该与WebKit版本保持一致
        safari_version = webkit_version  # 确保Safari版本与WebKit版本一致
        webview_version = random.choice(webview_mapping[android_version])
        mozilla_version = random.choice(mozilla_versions)
        sdk_int = sdk_mapping[android_version]

        # 随机选择构建版本
        build_version = random.choice(self.build_versions[android_version])

        return {
            "chrome_version": chrome_version,
            "webkit_version": webkit_version,
            "safari_version": safari_version,
            "webview_version": webview_version,
            "sdk_int": sdk_int,
            "build_version": build_version,
            "mozilla_version": mozilla_version
        }
    
    def generate_serial_number(self, brand: str) -> str:
        """生成硬件序列号"""
        if brand == "Samsung":
            # Samsung格式：R58N + 7位字母数字
            return "R58N" + ''.join(random.choices('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=7))
        elif brand == "Xiaomi":
            # Xiaomi格式：随机15位字母数字
            return ''.join(random.choices('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=15))
        elif brand == "OPPO":
            # OPPO格式：随机11位字母数字
            return ''.join(random.choices('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=11))
        else:
            # 通用格式：随机12位字母数字
            return ''.join(random.choices('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=12))
    
    def generate_device_fingerprint(self, device_info: Dict) -> str:
        """生成设备指纹"""
        # 使用设备关键信息生成唯一指纹
        fingerprint_data = f"{device_info['imei']}{device_info['android_id']}{device_info['mac_address']}{device_info['brand']}{device_info['model']}"
        return hashlib.md5(fingerprint_data.encode()).hexdigest()
    
    def generate_device_info_for_bilibili(self) -> Dict:
        """
        为哔哩哔哩生成完整的设备信息
        增强版本，支持更真实的设备特征生成
        """
        # 随机选择品牌和型号
        brand = random.choice(list(self.device_brands.keys()))
        model = random.choice(self.device_brands[brand])
        android_version = random.choice(self.android_versions)

        # 生成设备标识符 - 传入品牌信息以提高真实性
        imei = self.generate_imei(brand)
        android_id = self.generate_android_id()
        mac_address = self.generate_mac_address()
        serial_number = self.generate_serial_number(brand)

        # 生成其他设备信息
        cpu_abi = random.choice(["arm64-v8a", "armeabi-v7a"])
        screen_resolution = random.choice(["1080x2340", "1440x3200", "1080x2400", "720x1600"])
        memory_total = random.choice(["4096", "6144", "8192", "12288"])

        # 生成更真实的构建信息
        build_info = self._generate_build_info(brand, model, android_version)

        # 生成真实的浏览器信息
        browser_info = self.generate_browser_info(android_version)

        # 生成存储容量信息
        storage_total = random.choice(["32768", "65536", "131072", "262144", "524288"])  # 32GB, 64GB, 128GB, 256GB, 512GB

        device_info = {
            "brand": brand,
            "model": model,
            "android_version": android_version,
            "imei": imei,
            "android_id": android_id,
            "mac_address": mac_address,
            "serial_number": serial_number,
            "cpu_abi": cpu_abi,
            "screen_resolution": screen_resolution,
            "memory_total": memory_total,
            "storage_total": storage_total,
            # 添加构建信息
            "build_info": build_info,
            # 添加浏览器信息
            "browser_info": browser_info,
            # 添加构建ID（从build_info中提取）
            "build_id": build_info.get("build_id", "")
        }

        # 生成设备指纹
        device_info["device_fingerprint"] = self.generate_device_fingerprint(device_info)

        # 检查唯一性并保存到数据库
        if self._ensure_uniqueness(device_info):
            self._save_to_database(device_info)

        return device_info

    def _generate_build_info(self, brand: str, model: str, android_version: str) -> Dict:
        """生成构建信息，用于更真实的设备模拟"""
        import time

        # 生成构建时间 (30天到365天前)
        build_time = int(time.time()) - random.randint(86400 * 30, 86400 * 365)

        # 生成构建ID
        build_id = f"{brand.upper()}{random.randint(100, 999)}"

        # 生成增量版本号
        incremental = str(random.randint(1000000, 9999999))

        return {
            "build_id": build_id,
            "build_time": build_time,
            "incremental": incremental,
            "fingerprint": f"{brand}/{model.lower()}/{model.lower()}:{android_version}/{build_id}/{incremental}:user/release-keys"
        }
    
    def _ensure_uniqueness(self, device_info: Dict) -> bool:
        """确保设备信息的唯一性"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查关键字段是否已存在
        cursor.execute('''
            SELECT COUNT(*) FROM devices 
            WHERE imei = ? OR android_id = ? OR mac_address = ? OR device_fingerprint = ?
        ''', (device_info['imei'], device_info['android_id'], 
              device_info['mac_address'], device_info['device_fingerprint']))
        
        count = cursor.fetchone()[0]
        conn.close()
        
        return count == 0
    
    def _save_to_database(self, device_info: Dict):
        """保存设备信息到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO devices (
                    device_fingerprint, brand, model, android_version,
                    imei, android_id, mac_address, serial_number,
                    cpu_abi, screen_resolution, memory_total, storage_total, build_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                device_info['device_fingerprint'],
                device_info['brand'],
                device_info['model'],
                device_info['android_version'],
                device_info['imei'],
                device_info['android_id'],
                device_info['mac_address'],
                device_info['serial_number'],
                device_info['cpu_abi'],
                device_info['screen_resolution'],
                device_info['memory_total'],
                device_info['storage_total'],
                device_info['build_id']
            ))
            conn.commit()
        except sqlite3.IntegrityError:
            # 如果有重复，重新生成
            pass
        finally:
            conn.close()
    
    def get_device_count(self) -> int:
        """获取数据库中的设备数量"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM devices')
        count = cursor.fetchone()[0]
        conn.close()
        return count
    
    def get_device_statistics(self) -> Dict:
        """获取设备统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取总数
        cursor.execute('SELECT COUNT(*) FROM devices')
        total_count = cursor.fetchone()[0]
        
        # 获取最早和最新的生成时间
        cursor.execute('SELECT MIN(created_at), MAX(created_at) FROM devices')
        first_time, last_time = cursor.fetchone()
        
        conn.close()
        
        return {
            "total_devices": total_count,
            "first_generated": first_time,
            "last_generated": last_time
        }


if __name__ == "__main__":
    # 测试设备指纹生成
    fingerprint_gen = AndroidDeviceFingerprint()
    
    print("🔬 Android设备指纹生成测试")
    print("=" * 50)
    
    device_info = fingerprint_gen.generate_device_info_for_bilibili()
    
    print(f"✓ 设备品牌: {device_info['brand']}")
    print(f"✓ 设备型号: {device_info['model']}")
    print(f"✓ Android版本: {device_info['android_version']}")
    print(f"✓ IMEI: {device_info['imei']}")
    print(f"✓ Android ID: {device_info['android_id']}")
    print(f"✓ MAC地址: {device_info['mac_address']}")
    print(f"✓ 硬件序列号: {device_info['serial_number']}")
    print(f"✓ 设备指纹: {device_info['device_fingerprint']}")
    print(f"✓ CPU架构: {device_info['cpu_abi']}")
    print(f"✓ 屏幕分辨率: {device_info['screen_resolution']}")
    print(f"✓ 内存大小: {device_info['memory_total']}MB")
    
    # 显示统计信息
    stats = fingerprint_gen.get_device_statistics()
    print(f"\n📊 数据库统计:")
    print(f"  总设备数: {stats['total_devices']} 个")
    if stats['first_generated']:
        print(f"  首次生成: {stats['first_generated']}")
        print(f"  最近生成: {stats['last_generated']}")
    
    print("\n✅ 设备指纹生成测试完成")
