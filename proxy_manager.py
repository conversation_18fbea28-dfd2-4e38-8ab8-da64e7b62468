#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理器
支持动态代理API，实现代理获取、测试、轮换等功能
"""

import requests
import time
import random
from typing import Dict, Optional, List


class ProxyManager:
    """代理管理器，支持动态代理API"""
    
    def __init__(self, api_url: str = None, session_locked: bool = False):
        """
        初始化代理管理器

        Args:
            api_url: 代理API地址
            session_locked: 是否锁定会话代理（整个会话使用同一个代理）
        """
        self.api_url = api_url
        self.current_proxy = None
        self.proxy_history = []  # 记录使用过的代理
        self.failed_proxies = set()  # 记录失效的代理
        self.max_retry_count = 3  # 最大重试次数
        self.session_locked = session_locked  # 会话锁定标志
        self.locked_proxy = None  # 锁定的代理
        
    def set_api_url(self, api_url: str):
        """设置代理API地址"""
        self.api_url = api_url
        
    def get_proxy(self) -> Optional[Dict]:
        """
        从API获取新的代理

        Returns:
            代理信息字典，包含ip、port、protocol等字段
        """
        # 如果启用了会话锁定且已有锁定代理，直接返回锁定代理
        if self.session_locked and self.locked_proxy:
            print(f"🔒 使用会话锁定代理: {self.locked_proxy['ip']}:{self.locked_proxy['port']}")
            return self.locked_proxy

        if not self.api_url:
            return None

        try:
            response = requests.get(self.api_url, timeout=10)
            if response.status_code == 200:
                proxy_text = response.text.strip()

                # 解析代理信息 (格式: ip:port)
                if ':' in proxy_text:
                    ip, port = proxy_text.split(':', 1)
                    proxy_info = {
                        'ip': ip.strip(),
                        'port': port.strip(),
                        'protocol': 'socks5',
                        'created_time': time.time(),
                        'retry_count': 0
                    }

                    # 检查是否是已失效的代理
                    proxy_key = f"{proxy_info['ip']}:{proxy_info['port']}"
                    if proxy_key not in self.failed_proxies:
                        self.current_proxy = proxy_info
                        self.proxy_history.append(proxy_info.copy())

                        # 如果启用了会话锁定，锁定这个代理
                        if self.session_locked and not self.locked_proxy:
                            self.locked_proxy = proxy_info.copy()
                            print(f"🔒 代理已锁定到会话: {proxy_info['ip']}:{proxy_info['port']}")

                        return proxy_info
                    else:
                        # 如果是失效代理，尝试重新获取（但不在会话锁定模式下）
                        if not self.session_locked:
                            return self.get_proxy()

        except Exception as e:
            print(f"获取代理失败: {e}")

        return None
    
    def test_proxy(self, proxy_info: Dict) -> bool:
        """
        测试代理连接性
        
        Args:
            proxy_info: 代理信息
            
        Returns:
            True表示代理可用，False表示不可用
        """
        try:
            # 构建代理配置
            proxy_url = f"socks5://{proxy_info['ip']}:{proxy_info['port']}"
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            # 测试多个URL以确保代理稳定性
            test_urls = [
                "http://httpbin.org/ip",
                "https://api.ipify.org?format=json",
                "http://ip-api.com/json"
            ]
            
            for test_url in test_urls:
                try:
                    response = requests.get(test_url, proxies=proxies, timeout=10)
                    if response.status_code == 200:
                        return True
                except:
                    continue
                    
            return False
            
        except Exception as e:
            print(f"代理测试失败: {e}")
            return False
    
    def mark_proxy_failed(self, proxy_info: Dict):
        """
        标记代理为失效

        Args:
            proxy_info: 代理信息
        """
        if proxy_info:
            proxy_key = f"{proxy_info['ip']}:{proxy_info['port']}"
            self.failed_proxies.add(proxy_key)
            print(f"代理已标记为失效: {proxy_key}")

            # 如果失效的代理是当前锁定的代理，解除锁定以允许获取新代理
            if (self.session_locked and self.locked_proxy and
                self.locked_proxy['ip'] == proxy_info['ip'] and
                self.locked_proxy['port'] == proxy_info['port']):
                print(f"🔓 锁定代理失效，解除会话锁定以获取新代理")
                self.locked_proxy = None
    
    def get_proxy_with_retry(self, max_attempts: int = 3) -> Optional[Dict]:
        """
        获取代理并进行重试

        Args:
            max_attempts: 最大尝试次数

        Returns:
            可用的代理信息
        """
        for attempt in range(max_attempts):
            proxy_info = self.get_proxy()
            if proxy_info and self.test_proxy(proxy_info):
                return proxy_info
            elif proxy_info:
                self.mark_proxy_failed(proxy_info)

                # 如果是会话锁定模式且锁定代理失效，尝试获取新代理
                if self.session_locked and not self.locked_proxy:
                    print(f"🔄 会话锁定模式：尝试获取新代理替换失效代理")
                    continue

            # 等待一段时间后重试
            if attempt < max_attempts - 1:
                time.sleep(random.uniform(1, 3))

        return None
    
    def get_requests_proxies(self, proxy_info: Dict = None) -> Dict:
        """
        获取requests库使用的代理配置
        
        Args:
            proxy_info: 代理信息，如果为None则使用当前代理
            
        Returns:
            requests库的代理配置字典
        """
        if proxy_info is None:
            proxy_info = self.current_proxy
            
        if not proxy_info:
            return {}
            
        proxy_url = f"socks5://{proxy_info['ip']}:{proxy_info['port']}"
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def get_proxy_stats(self) -> Dict:
        """
        获取代理使用统计
        
        Returns:
            统计信息字典
        """
        return {
            'total_used': len(self.proxy_history),
            'failed_count': len(self.failed_proxies),
            'current_proxy': self.current_proxy,
            'success_rate': (len(self.proxy_history) - len(self.failed_proxies)) / max(len(self.proxy_history), 1) * 100
        }
    
    def clear_failed_proxies(self):
        """清除失效代理记录"""
        self.failed_proxies.clear()
        print("已清除失效代理记录")
    
    def is_enabled(self) -> bool:
        """检查代理管理器是否启用"""
        return self.api_url is not None and len(self.api_url.strip()) > 0

    def enable_session_lock(self):
        """启用会话锁定模式"""
        self.session_locked = True
        print("🔒 会话锁定模式已启用")

    def disable_session_lock(self):
        """禁用会话锁定模式"""
        self.session_locked = False
        self.locked_proxy = None
        print("🔓 会话锁定模式已禁用")

    def is_session_locked(self) -> bool:
        """检查是否启用了会话锁定"""
        return self.session_locked

    def get_locked_proxy(self) -> Optional[Dict]:
        """获取锁定的代理"""
        return self.locked_proxy

    def force_unlock_session(self):
        """强制解锁会话（紧急情况使用）"""
        if self.session_locked:
            print(f"⚠️ 强制解锁会话代理: {self.locked_proxy['ip']}:{self.locked_proxy['port']}" if self.locked_proxy else "无锁定代理")
            self.locked_proxy = None
            print("🔓 会话已强制解锁")


class ProxyRotator:
    """代理轮换器，支持多个代理管理器"""
    
    def __init__(self, proxy_managers: List[ProxyManager] = None):
        """
        初始化代理轮换器
        
        Args:
            proxy_managers: 代理管理器列表
        """
        self.proxy_managers = proxy_managers or []
        self.current_manager_index = 0
        
    def add_proxy_manager(self, proxy_manager: ProxyManager):
        """添加代理管理器"""
        self.proxy_managers.append(proxy_manager)
        
    def get_next_proxy(self) -> Optional[Dict]:
        """
        获取下一个可用代理
        
        Returns:
            代理信息
        """
        if not self.proxy_managers:
            return None
            
        # 轮换代理管理器
        for _ in range(len(self.proxy_managers)):
            manager = self.proxy_managers[self.current_manager_index]
            self.current_manager_index = (self.current_manager_index + 1) % len(self.proxy_managers)
            
            proxy_info = manager.get_proxy_with_retry()
            if proxy_info:
                return proxy_info
                
        return None
    
    def mark_proxy_failed(self, proxy_info: Dict):
        """标记代理失效"""
        for manager in self.proxy_managers:
            manager.mark_proxy_failed(proxy_info)


# 全局代理管理器实例
_global_proxy_manager = None

def get_global_proxy_manager() -> ProxyManager:
    """获取全局代理管理器实例"""
    global _global_proxy_manager
    if _global_proxy_manager is None:
        _global_proxy_manager = ProxyManager()
    return _global_proxy_manager

def set_global_proxy_api(api_url: str):
    """设置全局代理API地址"""
    manager = get_global_proxy_manager()
    manager.set_api_url(api_url)
