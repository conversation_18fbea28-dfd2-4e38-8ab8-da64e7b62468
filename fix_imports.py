#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入修复脚本
检查并修复优化过程中可能遗漏的导入问题
"""

def test_imports():
    """测试所有关键导入"""
    print("🔍 测试关键导入...")
    
    try:
        # 测试基础导入
        from urllib.parse import urlparse, parse_qs, urlencode
        print("✅ urllib.parse 导入正常")
        
        import datetime
        print("✅ datetime 导入正常")
        
        # 测试项目模块导入
        from config import config
        print("✅ config 模块导入正常")
        
        from utils import validation_utils, network_utils, database_utils
        print("✅ utils 模块导入正常")
        
        from bilibili_api_utils import BilibiliAPIUtils
        print("✅ bilibili_api_utils 模块导入正常")
        
        # 测试验证码解析功能
        test_url = "https://www.bilibili.com/h5/project-msg-auth/verify?ct=geetest&recaptcha_token=test&gee_gt=test_gt&gee_challenge=test_challenge"
        parsed_url = urlparse(test_url)
        query_params = parse_qs(parsed_url.query)
        
        gt = query_params.get('gee_gt', [None])[0]
        challenge = query_params.get('gee_challenge', [None])[0]
        
        if gt and challenge:
            print("✅ 验证码URL解析功能正常")
        else:
            print("❌ 验证码URL解析功能异常")
            
        # 测试高危账号检测
        test_data = {
            'status': 2,
            'message': '账号存在高危异常行为',
            'url': 'https://example.com'
        }
        
        high_risk_check = validation_utils.check_high_risk_account(test_data)
        if high_risk_check['is_high_risk']:
            print("✅ 高危账号检测功能正常")
        else:
            print("❌ 高危账号检测功能异常")
            
        # 测试网络连接
        connectivity = network_utils.test_network_connectivity()
        print(f"✅ 网络连接测试完成: DNS={connectivity['dns']}, TCP={connectivity['tcp']}, HTTP={connectivity['http']}")
        
        print("\n🎉 所有关键功能测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 功能测试错误: {e}")
        return False

def check_gui_imports():
    """检查GUI模块的导入"""
    print("\n🖥️ 测试GUI模块导入...")
    
    try:
        # 这里只测试导入，不实际创建GUI
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QThread, pyqtSignal
        from PyQt5.QtGui import QFont, QIcon
        print("✅ PyQt5 导入正常")
        
        # 测试主要的GUI类导入（不实例化）
        import bilibili_sms_gui
        print("✅ bilibili_sms_gui 模块导入正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ GUI导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI测试错误: {e}")
        return False

def main():
    """主函数"""
    print("🔧 导入修复验证脚本")
    print("=" * 50)
    
    # 测试基础功能
    basic_ok = test_imports()
    
    # 测试GUI功能
    gui_ok = check_gui_imports()
    
    print("\n" + "=" * 50)
    if basic_ok and gui_ok:
        print("🎉 所有导入测试通过！验证码功能应该可以正常使用了。")
        print("\n💡 建议:")
        print("1. 重新启动GUI应用程序")
        print("2. 测试验证码获取功能")
        print("3. 如果还有问题，请检查具体的错误信息")
    else:
        print("❌ 部分测试失败，请检查错误信息并修复")
        
    return basic_ok and gui_ok

if __name__ == "__main__":
    main()
